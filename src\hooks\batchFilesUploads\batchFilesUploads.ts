import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";
import useDataService from "../../services/useDataService";
import { GET_BATCH_AVAILIBILITY_STATUS, GETPRESIGNED_URL } from "../../constants/urls";
import axios from "axios";

const getPresignedUrlApi = async (fileData: any) => {
  const response = await useDataService.postService(`${GETPRESIGNED_URL}`, fileData);
  return response;
};

export const usePresignedUrl = (onSuccess?: (data: any) => void, onError?: (error: Error) => void) => {
  return useMutation({
    mutationFn: getPresignedUrlApi,
    onSuccess,
    onError,
  });
};

const uploadFileUsingPresignedUrl = async (fileData: any) => {
  const result = await axios.put(fileData.upload_url, fileData.file.file);
  return result.data;
};

export const uploadPreSignedUrl = (onSuccess?: (data: any) => void, onError?: (error: Error) => void) => {
  return useMutation({
    mutationFn: uploadFileUsingPresignedUrl,
    onSuccess,
    onError,
  });
};

export const useGetBatchAvailibilityStatus = (batchId: string) => {
  return useQuery<any, Error>({
    queryKey: [],
    queryFn: async () => {
      const endpoint = `${GET_BATCH_AVAILIBILITY_STATUS + batchId}`;
      return await useDataService.getService(endpoint);
    },
    enabled: true,
    staleTime: 0,
  });
};

